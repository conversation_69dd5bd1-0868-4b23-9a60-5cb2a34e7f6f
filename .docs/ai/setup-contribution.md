# Codebase Setup Assistant

Write the ./.docs/1.COLLABORATION.md based on the template.
Do not change the [.docs/ai/templates/__COLLABORATION.md] but follow the template. Only updating the required areas.

## 📚 REQUIRED DOCUMENTATION

NOTE: Always follow the [.docs/ai/templates/*.md] when writing any docs.

- Policies and Rules for collaborating should be defined in [.docs/1.COLLABORATION.md]

## 🔄 MANDATORY SYNC PROTOCOL

1. Before making changes read the current [.docs/ai/templates/*.md].

2. Check if a [.docs/1.COLLABORATION.md] is not already present, else clone the [.docs/ai/templates/__COLLABORATION.md] as a starter.

2. Read the [.docs/1.COLLABORATION.md].

3. Analyze the project code in [./src]

4. Update the [.docs/1.COLLABORATION.md] based on analysis from this project.
